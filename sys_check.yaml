---
- name: 系统巡检报告生成
  hosts: all
  become: yes
  gather_facts: yes

  tasks:
    - name: 显示巡检开始信息
      debug:
        msg: "############################################################## 开始巡检节点: {{ ansible_host }} #########################################################################"

    - name: 收集并显示系统基本信息
      block:
        - name: 获取系统时间
          command: "date"
          register: system_time
          changed_when: false

        - name: 获取登录用户信息
          command: "who"
          register: logged_in_users
          changed_when: false

        - name: 显示系统基本信息
          debug:
            msg: |
              ############################################################## 系统基本信息 #########################################################################
              系统主机名：{{ ansible_hostname }}
              系统版本：{{ ansible_distribution }} {{ ansible_distribution_version }}
              系统内核：{{ ansible_kernel }}
              系统时间：{{ system_time.stdout }}
              系统运行时间：{{ ansible_uptime_seconds }}秒
              当前登录用户：{{ logged_in_users.stdout.split('\n') | first | regex_replace('\\s+.*', '') | default('未知') }}

    - name: 显示CPU监控标题
      debug:
        msg: "############################################################## CPU监控 (采样3次) #########################################################################"

    - name: 收集并显示CPU信息
      block:
        - name: 采样CPU使用率(3次)
          shell: |
            for i in {1..3}; do
              TERM=dumb top -bn1 | grep "Cpu(s)" | awk -F, '{gsub(/%/, "", $1); gsub(/%/, "", $2); gsub(/%/, "", $4); gsub(/%/, "", $6); print "CPU总使用率："100-$4"% | CPU用户空间："$2"% | CPU系统空间："$4"% | CPU IO等待："$6"%"}'
              sleep 1
            done
          register: cpu_usage_samples
          changed_when: false
          environment:
            TERM: dumb

        - name: 显示CPU信息
          debug:
            msg: "{{ cpu_usage_samples.stdout }}"

    - name: 显示内存监控标题
      debug:
        msg: "############################################################## 内存监控 #########################################################################"

    - name: 收集并显示内存信息
      block:
        - name: 获取内存信息
          shell: |
            free -h | awk '/Mem:/ {print "系统总内存：" $2 " | 内存已用：" $3 " | 内存可用：" $4 " | 内存缓存：" $6}'
          register: mem_info
          changed_when: false

        - name: 显示内存信息
          debug:
            msg: "{{ mem_info.stdout }}"

    - name: 显示磁盘监控标题
      debug:
        msg: "############################################################## 磁盘监控 #########################################################################"

    - name: 收集并显示磁盘信息
      block:
        - name: 获取磁盘使用情况
          shell: |
            df -h | awk 'NR>1 {print "磁盘挂载点：" $6 " | 磁盘总量：" $2 " | 磁盘已用：" $3 " | 磁盘可用：" $4 " | 磁盘使用率：" $5}'
          register: disk_usage
          changed_when: false

        - name: 显示磁盘信息
          debug:
            msg: "{{ disk_usage.stdout }}"

    - name: 显示负载监控标题
      debug:
        msg: "############################################################## 系统负载 #########################################################################"

    - name: 收集并显示负载信息
      block:
        - name: 获取系统负载
          shell: |
            echo "系统1分钟负载：$(cat /proc/loadavg | awk '{print $1}')"
            echo "系统5分钟负载：$(cat /proc/loadavg | awk '{print $2}')"
            echo "系统15分钟负载：$(cat /proc/loadavg | awk '{print $3}')"
            echo "系统CPU核心数：$(nproc)"
          register: load_info
          changed_when: false

        - name: 显示负载信息
          debug:
            msg: "{{ load_info.stdout }}"

    - name: 显示网络监控标题
      debug:
        msg: "############################################################## 网络连接状态 #########################################################################"

    - name: 收集并显示网络信息
      block:
        - name: 获取TCP连接摘要
          shell: |
            echo "网络TCP连接摘要："
            ss -s | grep -E "LISTEN|ESTAB|TIME-WAIT" || echo "无法获取连接摘要"
          register: tcp_summary
          changed_when: false

        - name: 获取监听端口
          shell: |
            echo "网络监听端口："
            ss -tulnp | grep LISTEN | awk '{print "端口协议：" $1 " | 监听地址：" $5 " | 进程信息：" $7}' || echo "无监听端口"
          register: listening_ports
          changed_when: false

        - name: 显示网络信息
          debug:
            msg: |
              {{ tcp_summary.stdout }}
              {{ listening_ports.stdout }}

    - name: 显示监控结束标记
      debug:
        msg: "############################################################## 监控结束 #########################################################################"
